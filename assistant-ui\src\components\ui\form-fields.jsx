import React from 'react';
import { Input } from './input';
import { Label } from './label';

// Simple text utility function to replace cn from utils
const cn = (...classes) => classes.filter(Boolean).join(' ');

// Simple Textarea component
const Textarea = ({ className, ...props }) => {
  return (
    <textarea
      className={cn(
        "border-slate-200 dark:border-slate-700 dark:bg-slate-800/60 placeholder:text-slate-400 focus-visible:border-blue-400 focus-visible:ring-blue-500/30 aria-invalid:ring-red-500/20 dark:aria-invalid:ring-red-500/40 aria-invalid:border-red-500 flex min-h-16 w-full rounded-md border bg-slate-50 px-3 py-2 text-base shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className
      )}
      {...props}
    />
  );
};

// Simple Checkbox component
const Checkbox = ({ className, ...props }) => {
  return (
    <input
      type="checkbox"
      className={cn(
        "peer border-slate-300 dark:border-slate-600 dark:bg-slate-800/60 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:text-white size-4 shrink-0 rounded-[4px] border shadow-xs transition-all outline-none focus-visible:ring-[3px] focus-visible:ring-blue-500/30 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    />
  );
};

// Simple Select components
const Select = ({ children, value, onValueChange, ...props }) => {
  return (
    <select
      value={value}
      onChange={(e) => onValueChange && onValueChange(e.target.value)}
      className="border-slate-200 dark:border-slate-700 dark:bg-slate-800/60 flex h-9 w-full rounded-md border bg-slate-50 px-3 py-1 text-sm shadow-xs transition-all outline-none focus-visible:border-blue-400 focus-visible:ring-blue-500/30 focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50"
      {...props}
    >
      {children}
    </select>
  );
};

const SelectTrigger = ({ className, children, ...props }) => {
  return (
    <div className={cn("relative", className)} {...props}>
      {children}
    </div>
  );
};

const SelectValue = ({ placeholder }) => {
  return <span>{placeholder}</span>;
};

const SelectContent = ({ children }) => {
  return <>{children}</>;
};

const SelectGroup = ({ children }) => {
  return <>{children}</>;
};

const SelectItem = ({ value, children }) => {
  return <option value={value}>{children}</option>;
};

const SelectLabel = () => null;

// Simple Form components
const FormField = ({ name, render, control }) => {
  return render({ field: { name, ...control } });
};

const FormItem = ({ children, className }) => {
  return <div className={cn("space-y-2", className)}>{children}</div>;
};

const FormLabel = ({ children }) => {
  return <Label>{children}</Label>;
};

const FormControl = ({ children }) => {
  return <>{children}</>;
};

const FormDescription = ({ children }) => {
  return <p className="text-slate-500 dark:text-slate-400 text-sm">{children}</p>;
};

const FormMessage = ({ children }) => {
  return children ? <p className="text-red-500 dark:text-red-400 text-sm">{children}</p> : null;
};

/**
 * StandardInput - A consistent text input with label and optional description
 */
export const StandardInput = ({ 
  id, 
  label, 
  description, 
  className = "", 
  containerClassName = "", 
  ...props 
}) => {
  return (
    <div className={`space-y-2 ${containerClassName}`}>
      {label && (
        <Label htmlFor={id} className="text-sm font-medium text-slate-700 dark:text-slate-300">
          {label}
        </Label>
      )}
      <Input 
        id={id}
        className={`w-full bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 focus-visible:ring-blue-500/30 transition-all duration-200 ${className}`}
        {...props}
      />
      {description && (
        <p className="text-slate-500 dark:text-slate-400 text-xs">
          {description}
        </p>
      )}
    </div>
  );
};

/**
 * StandardSelect - A consistent dropdown select with label and optional description
 */
export const StandardSelect = ({
  id,
  label,
  description,
  options,
  placeholder = "Select an option",
  className = "",
  containerClassName = "",
  ...props
}) => {
  return (
    <div className={`space-y-2 ${containerClassName}`}>
      {label && (
        <Label htmlFor={id} className="text-sm font-medium text-slate-700 dark:text-slate-300">
          {label}
        </Label>
      )}
      <div className="relative">
        <Select {...props}>
          <SelectTrigger id={id} className={`w-full bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 focus-visible:ring-blue-500/30 transition-all duration-200 ${className}`}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 shadow-lg rounded-md overflow-hidden">
            <SelectGroup>
              {options?.map(option => (
                <SelectItem key={option.value} value={option.value} className="hover:bg-slate-100 dark:hover:bg-slate-700">
                  {option.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 pointer-events-none">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.5 4.5L6 8L9.5 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      {description && (
        <p className="text-slate-500 dark:text-slate-400 text-xs">
          {description}
        </p>
      )}
    </div>
  );
};

/**
 * StandardTextarea - A consistent textarea with label and optional description
 */
export const StandardTextarea = ({
  id,
  label,
  description,
  maxLength,
  value,
  className = "",
  containerClassName = "",
  ...props
}) => {
  return (
    <div className={`space-y-2 ${containerClassName}`}>
      <div className="flex justify-between items-center">
        {label && (
          <Label htmlFor={id} className="text-sm font-medium text-slate-700 dark:text-slate-300">
            {label}
          </Label>
        )}
        {maxLength && (
          <span className="text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-700/50 px-2 py-0.5 rounded-full">
            {value?.length || 0}/{maxLength}
          </span>
        )}
      </div>
      <Textarea 
        id={id}
        className={`min-h-24 resize-none bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 focus-visible:ring-blue-500/30 transition-all duration-200 ${className}`}
        maxLength={maxLength}
        value={value}
        {...props}
      />
      {description && (
        <p className="text-slate-500 dark:text-slate-400 text-xs">
          {description}
        </p>
      )}
    </div>
  );
};

/**
 * StandardCheckbox - A consistent checkbox with label and optional description
 */
export const StandardCheckbox = ({
  id,
  label,
  description,
  fancy = false,
  className = "",
  containerClassName = "",
  ...props
}) => {
  if (fancy) {
    return (
      <Label className={`hover:bg-slate-50 dark:hover:bg-slate-800/70 flex items-start gap-3 rounded-lg border border-slate-200 dark:border-slate-700 p-3 transition-colors has-[[aria-checked=true]]:border-blue-400 has-[[aria-checked=true]]:bg-blue-50 dark:has-[[aria-checked=true]]:border-blue-600 dark:has-[[aria-checked=true]]:bg-blue-900/20 ${containerClassName}`}>
        <Checkbox
          id={id}
          className={`data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500 data-[state=checked]:text-white dark:data-[state=checked]:border-blue-500 dark:data-[state=checked]:bg-blue-500 ${className}`}
          {...props}
        />
        <div className="grid gap-1.5 font-normal">
          {label && <p className="text-sm leading-none font-medium text-slate-700 dark:text-slate-300">{label}</p>}
          {description && <p className="text-slate-500 dark:text-slate-400 text-sm">{description}</p>}
        </div>
      </Label>
    );
  }
  
  return (
    <div className={`flex items-center gap-2 ${containerClassName}`}>
      <Checkbox id={id} className={`border-slate-300 dark:border-slate-600 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500 ${className}`} {...props} />
      <div>
        {label && <Label htmlFor={id} className="text-sm font-medium text-slate-700 dark:text-slate-300">{label}</Label>}
        {description && <p className="text-slate-500 dark:text-slate-400 text-xs">{description}</p>}
      </div>
    </div>
  );
};

// Export all internal components for use in the app
export {
  Textarea,
  Checkbox,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
};

/**
 * FormInputField - A form-integrated input field with validation
 */
export const FormInputField = ({
  name,
  control,
  label,
  description,
  ...props
}) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-slate-700 dark:text-slate-300">{label}</FormLabel>
          <FormControl>
            <Input 
              {...field} 
              {...props} 
              className="bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 focus-visible:ring-blue-500/30 transition-all duration-200"
            />
          </FormControl>
          {description && <FormDescription className="text-slate-500 dark:text-slate-400">{description}</FormDescription>}
          <FormMessage className="text-red-500 dark:text-red-400" />
        </FormItem>
      )}
    />
  );
};