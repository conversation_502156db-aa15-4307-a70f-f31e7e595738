/**
 * Comprehensive Save Manager
 * Handles all save/load operations with multiple fallback layers
 */

import { safeInvoke } from './tauriHelpers';

class SaveManager {
  constructor() {
    this.saveQueue = new Map();
    this.isProcessing = false;
    this.notificationCallback = null;
    this.lastSaveTime = new Date();
    this.saveCount = 0;
  }

  /**
   * Set notification callback for user feedback
   */
  setNotificationCallback(callback) {
    this.notificationCallback = callback;
  }

  /**
   * Show notification to user
   */
  notify(type, message, duration = 3000) {
    if (this.notificationCallback) {
      this.notificationCallback({ type, message, duration });
    }
    console.log(`[${type.toUpperCase()}] ${message}`);
  }

  /**
   * Save data with multiple fallback layers
   */
  async save(key, data, options = {}) {
    const { immediate = true, showNotification = true } = options;
    
    try {
      // Add metadata
      const saveData = {
        ...data,
        meta: {
          last_saved: new Date().toISOString(),
          save_count: ++this.saveCount,
          version: "1.0.0",
          key: key
        }
      };

      console.log(`💾 SaveManager: Saving ${key}`, saveData);

      let backendSuccess = false;
      let localStorageSuccess = false;

      // Primary: Try backend save
      try {
        const result = await safeInvoke('save_user_settings', saveData);
        backendSuccess = true;
        console.log(`✅ Backend save successful for ${key}:`, result);
      } catch (backendError) {
        console.warn(`❌ Backend save failed for ${key}:`, backendError.message || backendError);
        // Check if it's a Tauri availability issue
        if (backendError.message && backendError.message.includes('Tauri backend not available')) {
          console.log('🔄 Running in browser mode, backend unavailable');
        }
      }

      // Secondary: Always save to localStorage as backup
      try {
        localStorage.setItem(`collective_${key}`, JSON.stringify(saveData));
        localStorageSuccess = true;
        console.log(`✅ localStorage save successful for ${key}`);
      } catch (lsError) {
        console.warn(`❌ localStorage save failed for ${key}:`, lsError);
      }

      // Tertiary: sessionStorage fallback
      if (!localStorageSuccess) {
        try {
          sessionStorage.setItem(`collective_${key}`, JSON.stringify(saveData));
          console.log(`✅ sessionStorage save successful for ${key}`);
        } catch (ssError) {
          console.warn(`❌ sessionStorage save failed for ${key}:`, ssError);
        }
      }

      this.lastSaveTime = new Date();

      if (showNotification) {
        if (backendSuccess) {
          this.notify('success', `${key} saved successfully`);
        } else if (localStorageSuccess) {
          this.notify('warning', `${key} saved locally (backend unavailable)`);
        } else {
          this.notify('error', `Failed to save ${key}`);
        }
      }

      return backendSuccess || localStorageSuccess;

    } catch (error) {
      console.error(`💥 SaveManager: Critical save error for ${key}:`, error);
      if (showNotification) {
        this.notify('error', `Critical error saving ${key}`);
      }
      return false;
    }
  }

  /**
   * Load data with fallback layers
   */
  async load(key, defaultData = null) {
    try {
      console.log(`📥 SaveManager: Loading ${key}`);

      let loadedData = null;

      // Primary: Try backend load
      try {
        loadedData = await safeInvoke('load_user_settings');
        if (loadedData && typeof loadedData === 'object') {
          console.log(`✅ Backend load successful for ${key}:`, loadedData);
          return this.validateAndMerge(loadedData, defaultData);
        }
      } catch (backendError) {
        console.warn(`❌ Backend load failed for ${key}:`, backendError.message || backendError);
      }

      // Secondary: Try localStorage
      try {
        const lsData = localStorage.getItem(`collective_${key}`);
        if (lsData) {
          loadedData = JSON.parse(lsData);
          console.log(`✅ localStorage load successful for ${key}`);
          return this.validateAndMerge(loadedData, defaultData);
        }
      } catch (lsError) {
        console.warn(`❌ localStorage load failed for ${key}:`, lsError);
      }

      // Tertiary: Try sessionStorage
      try {
        const ssData = sessionStorage.getItem(`collective_${key}`);
        if (ssData) {
          loadedData = JSON.parse(ssData);
          console.log(`✅ sessionStorage load successful for ${key}`);
          return this.validateAndMerge(loadedData, defaultData);
        }
      } catch (ssError) {
        console.warn(`❌ sessionStorage load failed for ${key}:`, ssError);
      }

      // Return defaults if all else fails
      console.log(`🔄 Using default data for ${key}`);
      return defaultData;

    } catch (error) {
      console.error(`💥 SaveManager: Critical load error for ${key}:`, error);
      return defaultData;
    }
  }

  /**
   * Validate and merge loaded data with defaults
   */
  validateAndMerge(loadedData, defaultData) {
    if (!defaultData) return loadedData;
    
    try {
      // Deep merge with defaults to ensure all required fields exist
      const merged = this.deepMerge(defaultData, loadedData);
      console.log(`🔄 Data merged with defaults`);
      return merged;
    } catch (error) {
      console.warn(`⚠️ Data merge failed, using defaults:`, error);
      return defaultData;
    }
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Batch save multiple items
   */
  async saveBatch(items, options = {}) {
    const results = [];
    for (const [key, data] of Object.entries(items)) {
      const result = await this.save(key, data, { ...options, showNotification: false });
      results.push({ key, success: result });
    }
    
    const successCount = results.filter(r => r.success).length;
    if (options.showNotification !== false) {
      this.notify('success', `Saved ${successCount}/${results.length} items`);
    }
    
    return results;
  }

  /**
   * Clear all saved data (for reset functionality)
   */
  async clearAll() {
    try {
      // Clear localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('collective_')) {
          localStorage.removeItem(key);
        }
      });

      // Clear sessionStorage
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('collective_')) {
          sessionStorage.removeItem(key);
        }
      });

      this.notify('info', 'All local data cleared');
      return true;
    } catch (error) {
      console.error('Failed to clear data:', error);
      this.notify('error', 'Failed to clear data');
      return false;
    }
  }

  /**
   * Get save statistics
   */
  getStats() {
    return {
      lastSaveTime: this.lastSaveTime,
      saveCount: this.saveCount,
      queueSize: this.saveQueue.size
    };
  }
}

// Create singleton instance
const saveManager = new SaveManager();

export default saveManager;
