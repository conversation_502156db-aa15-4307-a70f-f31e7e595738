import React, { useState, useEffect } from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import { useSettings } from '../../contexts/SettingsContext';
import saveManager from '../../utils/SaveManager';
import { Bell, Check, X, Info, AlertTriangle, AlertCircle, CheckCircle } from 'lucide-react';

const NotificationSettings = () => {
  const { addNotification } = useNotifications();
  const { userSettings, updateSetting } = useSettings();
  
  const [notificationSettings, setNotificationSettings] = useState({
    enabled: true,
    position: 'bottom-right',
    duration: 3000,
    showSaveNotifications: true,
    showLoadNotifications: false,
    showErrorNotifications: true,
    showWarningNotifications: true,
    showInfoNotifications: true,
    showSuccessNotifications: true,
    soundEnabled: false,
    maxNotifications: 5
  });

  const [saveStatus, setSaveStatus] = useState({
    backend: 'unknown',
    localStorage: 'unknown',
    lastSave: null,
    saveCount: 0
  });

  // Load notification settings on mount
  useEffect(() => {
    const loadedSettings = userSettings?.notifications || {};
    setNotificationSettings(prev => ({ ...prev, ...loadedSettings }));
    
    // Get save manager stats
    const stats = saveManager.getStats();
    setSaveStatus(prev => ({
      ...prev,
      lastSave: stats.lastSaveTime,
      saveCount: stats.saveCount
    }));
  }, [userSettings]);

  // Test backend connection
  const testBackendConnection = async () => {
    try {
      setSaveStatus(prev => ({ ...prev, backend: 'testing' }));
      const success = await saveManager.save('test_connection', { test: true }, { showNotification: false });
      setSaveStatus(prev => ({ 
        ...prev, 
        backend: success ? 'connected' : 'failed',
        localStorage: 'connected' // If we got here, localStorage works
      }));
      
      addNotification({
        type: success ? 'success' : 'warning',
        message: success ? 'Backend connection successful' : 'Backend unavailable, using local storage'
      });
    } catch (error) {
      setSaveStatus(prev => ({ ...prev, backend: 'failed', localStorage: 'connected' }));
      addNotification({
        type: 'error',
        message: 'Connection test failed'
      });
    }
  };

  // Save notification settings
  const saveNotificationSettings = async () => {
    try {
      await updateSetting('notifications', notificationSettings);
      addNotification({
        type: 'success',
        message: 'Notification settings saved'
      });
    } catch (error) {
      addNotification({
        type: 'error',
        message: 'Failed to save notification settings'
      });
    }
  };

  // Test notification
  const testNotification = (type) => {
    const messages = {
      success: 'This is a success notification',
      error: 'This is an error notification',
      warning: 'This is a warning notification',
      info: 'This is an info notification'
    };

    addNotification({
      type,
      message: messages[type],
      duration: notificationSettings.duration
    });
  };

  // Clear save manager stats
  const clearStats = () => {
    saveManager.saveCount = 0;
    saveManager.lastSaveTime = new Date();
    setSaveStatus(prev => ({ ...prev, saveCount: 0, lastSave: new Date() }));
    addNotification({
      type: 'info',
      message: 'Save statistics cleared'
    });
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <X className="w-4 h-4 text-red-500" />;
      case 'testing':
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Bell className="w-5 h-5 text-primary" />
        <h2 className="text-xl font-semibold">Notification Settings</h2>
      </div>

      {/* Connection Status */}
      <div className="bg-card p-4 rounded-lg border">
        <h3 className="font-semibold mb-3">Save System Status</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Backend Connection:</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(saveStatus.backend)}
              <span className="capitalize">{saveStatus.backend}</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span>Local Storage:</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(saveStatus.localStorage)}
              <span className="capitalize">{saveStatus.localStorage}</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span>Total Saves:</span>
            <span>{saveStatus.saveCount}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Last Save:</span>
            <span>{saveStatus.lastSave ? saveStatus.lastSave.toLocaleTimeString() : 'Never'}</span>
          </div>
        </div>
        <div className="flex gap-2 mt-3">
          <button
            onClick={testBackendConnection}
            className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90"
          >
            Test Connection
          </button>
          <button
            onClick={clearStats}
            className="px-3 py-1 bg-secondary text-secondary-foreground rounded text-sm hover:bg-secondary/90"
          >
            Clear Stats
          </button>
        </div>
      </div>

      {/* Notification Preferences */}
      <div className="bg-card p-4 rounded-lg border">
        <h3 className="font-semibold mb-3">Notification Preferences</h3>
        
        <div className="space-y-4">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <label className="font-medium">Enable Notifications</label>
            <input
              type="checkbox"
              checked={notificationSettings.enabled}
              onChange={(e) => setNotificationSettings(prev => ({ ...prev, enabled: e.target.checked }))}
              className="w-4 h-4"
            />
          </div>

          {/* Position */}
          <div className="flex items-center justify-between">
            <label className="font-medium">Position</label>
            <select
              value={notificationSettings.position}
              onChange={(e) => setNotificationSettings(prev => ({ ...prev, position: e.target.value }))}
              className="px-3 py-1 border rounded bg-background"
            >
              <option value="top-right">Top Right</option>
              <option value="top-left">Top Left</option>
              <option value="bottom-right">Bottom Right</option>
              <option value="bottom-left">Bottom Left</option>
            </select>
          </div>

          {/* Duration */}
          <div className="flex items-center justify-between">
            <label className="font-medium">Duration (ms)</label>
            <input
              type="number"
              value={notificationSettings.duration}
              onChange={(e) => setNotificationSettings(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
              className="w-20 px-2 py-1 border rounded bg-background"
              min="1000"
              max="10000"
              step="500"
            />
          </div>

          {/* Notification Types */}
          <div className="space-y-2">
            <h4 className="font-medium">Show Notifications For:</h4>
            {[
              { key: 'showSaveNotifications', label: 'Save Operations', icon: CheckCircle },
              { key: 'showLoadNotifications', label: 'Load Operations', icon: Info },
              { key: 'showErrorNotifications', label: 'Errors', icon: AlertCircle },
              { key: 'showWarningNotifications', label: 'Warnings', icon: AlertTriangle },
              { key: 'showInfoNotifications', label: 'Information', icon: Info },
              { key: 'showSuccessNotifications', label: 'Success Messages', icon: CheckCircle }
            ].map(({ key, label, icon: Icon }) => (
              <div key={key} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon className="w-4 h-4" />
                  <span>{label}</span>
                </div>
                <input
                  type="checkbox"
                  checked={notificationSettings[key]}
                  onChange={(e) => setNotificationSettings(prev => ({ ...prev, [key]: e.target.checked }))}
                  className="w-4 h-4"
                />
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={saveNotificationSettings}
          className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
        >
          Save Notification Settings
        </button>
      </div>

      {/* Test Notifications */}
      <div className="bg-card p-4 rounded-lg border">
        <h3 className="font-semibold mb-3">Test Notifications</h3>
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => testNotification('success')}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            Test Success
          </button>
          <button
            onClick={() => testNotification('error')}
            className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
          >
            Test Error
          </button>
          <button
            onClick={() => testNotification('warning')}
            className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
          >
            Test Warning
          </button>
          <button
            onClick={() => testNotification('info')}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            Test Info
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
