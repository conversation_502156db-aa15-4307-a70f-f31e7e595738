import React, { useContext, useEffect, useRef, useState } from 'react';
import { ChatContext } from '../contexts/ChatProvider';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { FontAwesomeIcon } from '../components/icons/FontAwesome.jsx';
import {
  faPaperPlane,
  faRobot,
  faUser,
  faSpinner,
  faRefresh,
  faCog,
  faPaperclip,
  faMicrophone,
  faLightbulb,
  faInfoCircle
} from '../components/icons/FontAwesome.jsx';
import ChatSettings from './ChatSettings';

const ChatView = ({ addTab }) => {
  const {
    messages,
    input,
    setInput,
    isLoading,
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    isPreloading,
    modelError,
    preloadingError,
    fetchAvailableModels,
    preloadModel,
    handleSubmit,
    autoWelcome,
    showModelInfo,
    typingIndicators
  } = useContext(ChatContext);

  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const [showSettings, setShowSettings] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  // Suggested prompts for welcome screen
  const suggestedPrompts = [
    "Help me write a professional email",
    "Explain quantum computing in simple terms",
    "Create a meal plan for this week",
    "Debug this code snippet",
    "Brainstorm creative project ideas"
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleSuggestedPrompt = (prompt) => {
    setInput(prompt);
  };

  const renderHeader = () => {
    if (!showModelInfo) return null;

    return (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <FontAwesomeIcon icon={faRobot} className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-sm">
                Model: {selectedModel || 'No model selected'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Context: {messages.length * 50} tokens
              </span>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="h-8 w-8 p-0"
          >
            <FontAwesomeIcon icon={faCog} className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  const renderMessage = (message) => {
    if (!message || !message.id) return null;
    
    return (
      <div 
        key={message.id}
        className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
      >
        <div
          className={`max-w-[70%] rounded-2xl p-4 shadow-sm relative ${
            message.sender === 'user'
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-4'
              : message.isError
                ? 'bg-red-50 text-red-700 border border-red-200 mr-4'
                : 'bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 mr-4 border border-gray-200 dark:border-gray-700'
          }`}
        >
          {/* Message bubble tail */}
          <div className={`absolute top-4 w-3 h-3 transform rotate-45 ${
            message.sender === 'user'
              ? 'bg-blue-500 -right-1'
              : message.isError
                ? 'bg-red-50 -left-1 border-l border-b border-red-200'
                : 'bg-white dark:bg-gray-800 -left-1 border-l border-b border-gray-200 dark:border-gray-700'
          }`} />

          <div className="flex items-start gap-3">
            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
              message.sender === 'user'
                ? 'bg-white/20'
                : message.isError
                  ? 'bg-red-100'
                  : 'bg-gradient-to-r from-purple-500 to-blue-500'
            }`}>
              <FontAwesomeIcon
                icon={message.sender === 'user' ? faUser : faRobot}
                className={`h-4 w-4 ${
                  message.sender === 'user' ? 'text-white' :
                  message.isError ? 'text-red-600' : 'text-white'
                }`}
              />
            </div>

            <div className="flex-1 min-w-0">
              {message.isTyping ? (
                <div className="flex space-x-1 py-2">
                  <div className={`w-2 h-2 rounded-full animate-bounce ${
                    message.sender === 'user' ? 'bg-white/70' : 'bg-gray-400'
                  }`} style={{ animationDelay: '0ms' }} />
                  <div className={`w-2 h-2 rounded-full animate-bounce ${
                    message.sender === 'user' ? 'bg-white/70' : 'bg-gray-400'
                  }`} style={{ animationDelay: '150ms' }} />
                  <div className={`w-2 h-2 rounded-full animate-bounce ${
                    message.sender === 'user' ? 'bg-white/70' : 'bg-gray-400'
                  }`} style={{ animationDelay: '300ms' }} />
                </div>
              ) : (
                <div className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                  {message.content}
                </div>
              )}

              <div className={`text-xs mt-2 ${
                message.sender === 'user' ? 'text-white/70' :
                message.isError ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'
              }`}>
                {message.timestamp && new Date(message.timestamp).toLocaleTimeString()}
                {message.model_used && ` • ${message.model_used}`}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (showSettings) {
    return <ChatSettings onClose={() => setShowSettings(false)} />;
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Modern Header with Model Info */}
      {renderHeader()}

      {/* Main Chat Title Bar */}
      <div className="flex items-center justify-between px-6 py-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">Chat</h1>
        <div className="flex items-center gap-2">
          {addTab && (
            <Button variant="outline" size="sm" onClick={() => addTab({ id: 'settings', name: 'Settings', component: 'ChatSettings', icon: faCog })}>
              <FontAwesomeIcon icon={faCog} className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(true)}
            className="h-8 w-8 p-0"
          >
            <FontAwesomeIcon icon={faCog} className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-6 py-4 bg-gray-50 dark:bg-gray-900"
      >
        {messages.length === 0 && isPreloading && (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 animate-spin text-primary mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Warming up the model...
            </h3>
            <p className="text-muted-foreground max-w-md text-center">
              This may take a moment.
            </p>
          </div>
        )}
        {messages.length === 0 && preloadingError && (
          <div className="flex flex-col items-center justify-center h-full text-destructive">
            <h3 className="text-xl font-semibold mb-2">Error</h3>
            <p className="max-w-md text-center">{preloadingError}</p>
          </div>
        )}
        {messages.length === 0 && !isPreloading && !preloadingError && (
          <div className="flex flex-col items-center justify-center h-full px-4">
            {/* Welcome Header */}
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-lg">
                <FontAwesomeIcon icon={faRobot} className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-3">
                Welcome! How can I help you today?
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                I'm powered by {selectedModel || 'AI'} and ready to assist you with any questions or tasks.
              </p>
            </div>

            {/* Suggested Prompts */}
            <div className="w-full max-w-2xl">
              <div className="flex items-center gap-2 mb-4">
                <FontAwesomeIcon icon={faLightbulb} className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Suggested prompts to get started:
                </span>
              </div>

              <div className="grid gap-3">
                {suggestedPrompts.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestedPrompt(prompt)}
                    className="text-left p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-blue-300 hover:shadow-md transition-all duration-200 group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full group-hover:scale-125 transition-transform" />
                      <span className="text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                        {prompt}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
        {messages.map(renderMessage)}
        <div ref={messagesEndRef} className="h-px" />
      </div>

      {/* Enhanced Input Area */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Select 
                value={selectedModel} 
                onValueChange={(model) => {
                  setSelectedModel(model);
                  preloadModel(model);
                }}
                disabled={isLoadingModels || !!modelError || isLoading || isPreloading}
              >
                <SelectTrigger className="w-full">
                  <div className="flex items-center gap-2">
                    {isLoadingModels && !availableModels.length ? (
                      <span className="flex items-center gap-2">
                        <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 animate-spin" />
                        <span>Loading models...</span>
                      </span>
                    ) : modelError ? (
                      <span className="text-red-500">Error loading models</span>
                    ) : (
                      <>
                        <span>{selectedModel || 'Select a model'}</span>
                        {isPreloading && <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 animate-spin text-muted-foreground" title="Preloading model..." />}
                      </>
                    )}
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {modelError && (
                <p className="mt-1 text-xs text-red-500">{modelError}</p>
              )}
              {preloadingError && (
                <p className="mt-1 text-xs text-red-500">{preloadingError}</p>
              )}
            </div>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={fetchAvailableModels}
              disabled={isLoadingModels || isLoading || isPreloading}
              type="button"
            >
              <FontAwesomeIcon 
                icon={isLoadingModels ? faSpinner : faRefresh} 
                className={`h-4 w-4 ${isLoadingModels ? 'animate-spin' : ''}`} 
              />
            </Button>
          </div>
          
          {/* Enhanced Input Row */}
          <div className="flex items-end gap-3">
            {/* File Attachment Button */}
            <Button
              type="button"
              variant="outline"
              size="icon"
              className="h-12 w-12 rounded-xl"
              disabled={isLoading || isPreloading}
              title="Attach File"
            >
              <FontAwesomeIcon icon={faPaperclip} className="h-4 w-4" />
            </Button>

            {/* Voice Input Button */}
            <Button
              type="button"
              variant="outline"
              size="icon"
              className="h-12 w-12 rounded-xl"
              disabled={isLoading || isPreloading}
              title="Voice Input"
            >
              <FontAwesomeIcon icon={faMicrophone} className="h-4 w-4" />
            </Button>

            {/* Message Input */}
            <div className="flex-1 relative">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message..."
                disabled={isLoading || isPreloading || !selectedModel}
                className="h-12 pr-12 rounded-xl border-2 focus:border-blue-500 transition-colors"
              />
              {typingIndicators && isTyping && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                  </div>
                </div>
              )}
            </div>

            {/* Send Button */}
            <Button
              type="submit"
              disabled={!input.trim() || isLoading || isPreloading || !selectedModel}
              className="h-12 px-6 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
            >
              <FontAwesomeIcon
                icon={isLoading ? faSpinner : faPaperPlane}
                className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
              />
              {isLoading ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChatView;