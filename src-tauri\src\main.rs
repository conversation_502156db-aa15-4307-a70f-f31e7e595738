// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use log::{info, error};
use tauri_plugin_log::{Target, TargetKind};
use std::{env, fs, path::PathBuf};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn main() {
    tauri::Builder::default()
        .plugin(
            tauri_plugin_log::Builder::new()
                .targets([
                    Target::new(TargetKind::Stdout),
                    Target::new(TargetKind::Webview),
                ])
                .build(),
        )
        .setup(|_app| {
            // Write backend readiness sentinel as soon as the exe launches
            fn find_project_root() -> Option<PathBuf> {
                let mut dir = env::current_dir().ok()?;
                let mut max_up = 10;
                while max_up > 0 {
                    if dir.join("src-tauri").exists() {
                        return Some(dir);
                    }
                    if !dir.pop() {
                        break;
                    }
                    max_up -= 1;
                }
                None
            }

            if let Some(root) = find_project_root() {
                let logs_dir = root.join("Storage").join("System").join("logs");
                if let Err(e) = fs::create_dir_all(&logs_dir) {
                    error!("Failed to create logs dir for readiness signal: {}", e);
                } else {
                    let ready_file = logs_dir.join("backend_ready.txt");
                    match fs::write(&ready_file, "READY") {
                        Ok(_) => {
                            info!("Backend readiness signal written at: {}", ready_file.display());
                        }
                        Err(e) => error!("Failed to write backend readiness signal: {}", e),
                    }
                }
            } else {
                error!("Could not locate project root to write readiness signal");
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
