import React, { useState } from 'react';

// Local icons for select component
const ChevronDownIcon = ({ className = '', isOpen = false }) => (
  <svg
    className={`inline-block ${className} transition-transform ${isOpen ? 'rotate-180' : ''}`}
    fill="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M7.41 8.84L12 13.42l4.59-4.58L18 10.25l-6 6-6-6z" />
  </svg>
);

const CheckIcon = ({ className = '' }) => (
  <svg
    className={`inline-block ${className}`}
    fill="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
  </svg>
);

// Simple Select component without external dependencies
const Select = ({ value, onValueChange, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      {React.Children.map(children, child => {
        if (child.type === SelectTrigger) {
          return React.cloneElement(child, {
            onClick: () => setIsOpen(!isOpen),
            isOpen,
            value
          });
        }
        if (child.type === SelectContent && isOpen) {
          return React.cloneElement(child, {
            onSelect: (selectedValue) => {
              onValueChange(selectedValue);
              setIsOpen(false);
            },
            value
          });
        }
        return null;
      })}
    </div>
  );
};

const SelectTrigger = ({ className = '', children, onClick, isOpen, value, ...props }) => (
  <button
    type="button"
    onClick={onClick}
    className={`flex h-10 w-full items-center justify-between rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
    {...props}
  >
    {children}
    <ChevronDownIcon
      className="h-4 w-4 opacity-50"
      isOpen={isOpen}
    />
  </button>
);

const SelectValue = ({ placeholder, children }) => (
  <span className="text-gray-500 dark:text-gray-400">
    {children || placeholder}
  </span>
);

const SelectContent = ({ className = '', children, onSelect, value, ...props }) => (
  <div
    className={`absolute top-full left-0 right-0 z-50 mt-1 max-h-96 overflow-hidden rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-lg ${className}`}
    {...props}
  >
    <div className="p-1 max-h-96 overflow-y-auto">
      {React.Children.map(children, child => {
        if (child.type === SelectItem) {
          return React.cloneElement(child, {
            onSelect,
            isSelected: value === child.props.value
          });
        }
        return child;
      })}
    </div>
  </div>
);

const SelectItem = ({ className = '', children, value, onSelect, isSelected, ...props }) => (
  <div
    className={`relative flex w-full cursor-pointer select-none items-center rounded-sm py-2 pl-8 pr-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''} ${className}`}
    onClick={() => onSelect(value)}
    {...props}
  >
    {isSelected && (
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        <CheckIcon className="h-3 w-3" />
      </span>
    )}
    {children}
  </div>
);

const SelectLabel = ({ className = '', children, ...props }) => (
  <div className={`py-1.5 pl-8 pr-2 text-sm font-semibold text-gray-700 dark:text-gray-300 ${className}`} {...props}>
    {children}
  </div>
);

const SelectSeparator = ({ className = '', ...props }) => (
  <div className={`-mx-1 my-1 h-px bg-gray-200 dark:bg-gray-600 ${className}`} {...props} />
);

export {
  Select,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
};