import React from 'react';
import { useChat } from '../hooks/useChat';
import ModelSelector from './ModelSelector';

const Header = () => {
  const { selectedModel, models } = useChat();

  return (
    <div className="flex items-center justify-between p-4 border-b">
      <h1 className="text-xl font-bold">Chat</h1>
      <ModelSelector selectedModel={selectedModel} models={models} />
    </div>
  );
};

export default Header;