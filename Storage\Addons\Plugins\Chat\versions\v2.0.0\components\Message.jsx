import React from 'react';

const Message = ({ message }) => {
  const { sender, text } = message;
  const isUser = sender === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`rounded-lg px-4 py-2 ${isUser ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}`}>
        <p>{text}</p>
      </div>
    </div>
  );
};

export default Message;