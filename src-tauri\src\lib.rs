// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
mod settings_manager;
mod file_manager;
mod database;
mod plugin_manager;
mod ai_client;
mod extension_manager;
mod server_adapters;
mod command_processor;
mod system_metrics;

use log::{info, error};
use tauri::Emitter;
use std::panic;
use std::time::Duration;
use std::fs; // added
use std::env; // added

// Helper to find project root (directory containing "src-tauri")
fn project_root_dir() -> Option<std::path::PathBuf> {
    let mut current_dir = env::current_dir().ok()?;
    // Traverse up until we find a directory containing "src-tauri"
    loop {
        if current_dir.join("src-tauri").exists() {
            return Some(current_dir);
        }
        if !current_dir.pop() {
            return None;
        }
    }
}

#[derive(Debug, Clone, Copy)]
pub struct InitializationConfig {
    pub enable_plugins: bool,
    pub enable_chat: bool,
    pub enable_servers: bool,
    pub enable_models: bool,
}

impl Default for InitializationConfig {
    fn default() -> Self {
        Self {
            enable_plugins: true,
            enable_chat: true,
            enable_servers: true,
            enable_models: true,
        }
    }
}

#[tauri::command]
fn greet(name: &str) -> String {
    info!("Greet command called with name: {}", name);
    format!("Hello, {}! You've been greeted from Rust!", name)
}

pub fn run_with_config(config: InitializationConfig) -> Result<(), Box<dyn std::error::Error>> {
    // Set up panic hook for better error reporting
    panic::set_hook(Box::new(|panic_info| {
        error!("A panic occurred: {:?}", panic_info);
    }));

    if env_logger::try_init().is_err() {
        // Logger already initialized
    }
    
    info!("Starting The Collective application with config: {:?}", config);
    
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .manage(command_processor::CommandProcessorState::default())
        .manage(ai_client::AiClient::new("http://127.0.0.1:11435".to_string()))
        .setup(move |app| {
            info!("Initializing user preferences...");
            
            // Load user preferences
            // Load user preferences (currently unused, but keeping for future use)
            let _prefs = settings_manager::UserPreferences::load();
            info!("Successfully loaded user preferences");

            // Ensure all required directories exist
            if let Err(e) = settings_manager::ensure_directories() {
                error!("Failed to create required directories: {}", e);
                return Err(Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Failed to create required directories: {}", e)
                )));
            }

            // Write backend readiness sentinel file so external scripts can wait for backend to be fully initialized
            if let Some(root) = project_root_dir() {
                let logs_dir = root.join("Storage").join("System").join("logs");
                if let Err(e) = fs::create_dir_all(&logs_dir) {
                    error!("Failed to create logs dir for readiness signal: {}", e);
                } else {
                    let ready_file = logs_dir.join("backend_ready.txt");
                    match fs::write(&ready_file, "READY") {
                        Ok(_) => info!("Backend readiness signal written at: {}", ready_file.display()),
                        Err(e) => error!("Failed to write backend readiness signal: {}", e),
                    }
                }
            } else {
                error!("Could not locate project root to write readiness signal");
            }

            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                info!("=== STARTING COMPREHENSIVE INITIALIZATION ===");
                let _ = app_handle.emit("initialization_start", "Initializing system...");

                // 1. Initialize server profiles (if enabled)
                if config.enable_servers {
                    info!("Initializing server profiles...");
                    if let Err(e) = app_handle.emit("initialization_progress", "Loading server profiles...") {
                        error!("Failed to emit progress event: {}", e);
                    }
                    match settings_manager::get_server_profiles().await {
                        Ok(profiles) => {
                            info!("Successfully loaded {} server profiles", profiles.len());
                            if let Err(e) = app_handle.emit("initialization_progress", 
                                format!("Loaded {} server profiles", profiles.len())) {
                                error!("Failed to emit progress event: {}", e);
                            }
                        }
                        Err(e) => {
                            error!("Failed to load server profiles: {}", e);
                            let _ = app_handle.emit("initialization_progress", 
                                format!("Warning: Failed to load server profiles: {}", e));
                        }
                    }
                } else {
                    info!("Skipping server profiles initialization (disabled in config)");
                }

                // 2. Initialize plugins (if enabled)
                if config.enable_plugins {
                    info!("Initializing plugins...");
                    if let Err(e) = app_handle.emit("initialization_progress", "Loading plugins...") {
                        error!("Failed to emit progress event: {}", e);
                    }
                    match plugin_manager::get_plugins().await {
                        Ok(plugins) => {
                            info!("Successfully loaded {} plugins", plugins.len());
                            if let Err(e) = app_handle.emit("initialization_progress", 
                                format!("Loaded {} plugins", plugins.len())) {
                                error!("Failed to emit progress event: {}", e);
                            }
                        }
                        Err(e) => {
                            error!("Failed to load plugins: {}", e);
                            let _ = app_handle.emit("initialization_progress", 
                                format!("Warning: Failed to load plugins: {}", e));
                        }
                    }
                } else {
                    info!("Skipping plugins initialization (disabled in config)");
                }

                // 3. Initialize chat system (if enabled)
                if config.enable_chat {
                    info!("Initializing chat system...");
                    let _ = app_handle.emit("initialization_progress", "Initializing chat system...");
                    match database::initialize_chat_tables() {
                        Ok(_) => {
                            info!("Successfully initialized chat system");
                        }
                        Err(e) => {
                            error!("Failed to initialize chat system: {}", e);
                            let _ = app_handle.emit("initialization_progress", 
                                format!("Warning: Failed to initialize chat system: {}", e));
                        }
                    }
                } else {
                    info!("Skipping chat system initialization (disabled in config)");
                }

                // 4. Initialize models (if enabled)
                if config.enable_models {
                    info!("Initializing models...");
                    let _ = app_handle.emit("initialization_progress", "Initializing models...");
                    // Add model initialization code here
                    info!("Model initialization complete");
                } else {
                    info!("Skipping models initialization (disabled in config)");
                }

                info!("=== INITIALIZATION COMPLETE ===");
                let _ = app_handle.emit("initialization_complete", "System ready!");
                
                // Keep the task alive
                loop {
                    tokio::time::sleep(Duration::from_secs(3600)).await; // Sleep for 1 hour
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            // Commands from settings_manager
            settings_manager::load_user_settings,
            settings_manager::save_user_settings,
            settings_manager::get_plugins_path,
            settings_manager::set_plugins_path,
            settings_manager::get_servers_path,
            settings_manager::set_servers_path,
            settings_manager::get_models_path,
            settings_manager::set_models_path,
            // Commands from ai_client
            ai_client::get_server_status,
            ai_client::start_server_command,
            ai_client::stop_server_command,
            ai_client::get_models_command,
            ai_client::pull_model_command,
            ai_client::check_installation_command,
            ai_client::send_chat_message_command,
            ai_client::generate_welcome_message_command,
            ai_client::preload_model_command,
            // Commands from file_manager
            file_manager::browse_directory,
            file_manager::list_model_files,
            file_manager::get_file_content,
            file_manager::save_file_content,
            file_manager::delete_file,
            file_manager::copy_file,
            file_manager::move_file,
            file_manager::rename_file,
            file_manager::open_file_in_explorer,
            file_manager::dialog_open,
            // Commands from plugin_manager
            plugin_manager::get_plugins,
            plugin_manager::toggle_plugin,
            plugin_manager::set_active_plugin_version,
            plugin_manager::get_plugin_settings,
            plugin_manager::save_plugin_data,
            plugin_manager::get_plugin_data,
            plugin_manager::save_plugin_state,
            plugin_manager::load_plugin_state,
            plugin_manager::backup_plugin_data,
            // Commands from database
            database::get_chat_messages,
            database::clear_chat_messages,
            // New chat system commands
            database::save_chat_session,
            database::load_chat_sessions,
            database::save_chat_message,
            database::load_chat_messages,
            database::delete_chat_session,
            // Commands from command_processor
            command_processor::run_command,
            command_processor::check_command_status,
            command_processor::stop_command,
            // Commands from system_metrics
            system_metrics::get_system_metrics,
            // Server and Model Profile Management
            settings_manager::get_server_profiles,
            settings_manager::save_server_profile,
            settings_manager::delete_server_profile,
            settings_manager::toggle_server_profile,
            // Active profile management
            settings_manager::set_active_server_profile,
            settings_manager::get_active_server_profile,
            settings_manager::clear_active_server_profile,
            settings_manager::set_active_model_profile,
            settings_manager::get_active_model_profile,
            settings_manager::clear_active_model_profile,
            settings_manager::get_active_server_config,
            // Model profiles removed - using direct server queries instead
            // Path-related commands
            settings_manager::get_directory_info,
            settings_manager::get_indexed_directory,
            settings_manager::set_indexed_directory,
            settings_manager::get_mcps_path,
            settings_manager::set_mcps_path,
            settings_manager::get_apis_path,
            settings_manager::set_apis_path,
            settings_manager::get_servers_path,
            settings_manager::set_servers_path,
            settings_manager::get_system_prompts_path,
            settings_manager::set_system_prompts_path,
            settings_manager::get_logic_hub_path,
            settings_manager::set_logic_hub_path,
            settings_manager::get_logic_system_prompts_path,
            settings_manager::set_logic_system_prompts_path,
            settings_manager::get_logic_modals_path,
            settings_manager::set_logic_modals_path,
            settings_manager::get_logic_agents_path,
            settings_manager::set_logic_agents_path,
            settings_manager::get_extensions_path,
            settings_manager::set_extensions_path,
            extension_manager::get_extensions_registry,
            extension_manager::add_extension,
            extension_manager::update_extension,
            extension_manager::remove_extension,
            extension_manager::get_extension_stats,
            // Note: get_language_server_model_path doesn't exist, using set_models_path instead
            settings_manager::set_models_path,
            settings_manager::get_system_log_path,
            settings_manager::set_system_log_path,
            // Projection settings commands
            settings_manager::get_ios_projection_enabled,
            settings_manager::set_ios_projection_enabled,
            settings_manager::get_ios_device_name,
            settings_manager::set_ios_device_name,
            settings_manager::get_ios_projection_quality,
            settings_manager::set_ios_projection_quality,
            settings_manager::get_android_projection_enabled,
            settings_manager::set_android_projection_enabled,
            settings_manager::get_android_device_name,
            settings_manager::set_android_device_name,
            settings_manager::get_android_projection_quality,
            settings_manager::set_android_projection_quality,
            settings_manager::get_miracast_enabled,
            settings_manager::set_miracast_enabled,
            settings_manager::get_dlna_enabled,
            settings_manager::set_dlna_enabled,
            settings_manager::get_projection_local_only,
            settings_manager::set_projection_local_only,
            settings_manager::get_projection_port,
            settings_manager::set_projection_port,
            settings_manager::get_projection_protocol,
            settings_manager::set_projection_protocol
        ])
        .run(tauri::generate_context!())
        .map_err(|e| Box::new(e) as Box<dyn std::error::Error>)
}

pub fn run() -> Result<(), Box<dyn std::error::Error>> {
    // Start with all subsystems enabled
    let config = InitializationConfig::default();
    run_with_config(config)
}
