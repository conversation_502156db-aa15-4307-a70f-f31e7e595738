import React, { useContext } from 'react';
import { ChatContext } from '../contexts/ChatProvider';

const ChatSettings = () => {
  const { 
    autoWelcome,
    setAutoWelcome,
    enableStreaming,
    setEnableStreaming,
    showModelInfo,
    setShowModelInfo,
    autoScroll,
    setAutoScroll,
    typingIndicators,
    setTypingIndicators,
    theme,
    setTheme,
    fontSize,
    setFontSize
  } = useContext(ChatContext);

  return (
    <div className="container mx-auto p-4 h-full flex flex-col">
      <h1 className="text-2xl font-bold mb-4">Chat Settings</h1>
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">General</h2>
          <div className="flex items-center justify-between">
            <label htmlFor="auto_welcome">Auto-welcome message</label>
            <input id="auto_welcome" type="checkbox" checked={autoWelcome} onChange={(e) => setAutoWelcome(e.target.checked)} />
          </div>
          <div className="flex items-center justify-between">
            <label htmlFor="enable_streaming">Enable streaming responses</label>
            <input id="enable_streaming" type="checkbox" checked={enableStreaming} onChange={(e) => setEnableStreaming(e.target.checked)} />
          </div>
          <div className="flex items-center justify-between">
            <label htmlFor="show_model_info">Show model information</label>
            <input id="show_model_info" type="checkbox" checked={showModelInfo} onChange={(e) => setShowModelInfo(e.target.checked)} />
          </div>
        </div>
        <div>
          <h2 className="text-lg font-semibold">Appearance</h2>
          <div className="flex items-center justify-between">
            <label htmlFor="theme">Theme</label>
            <select id="theme" value={theme} onChange={(e) => setTheme(e.target.value)}>
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="system">System</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <label htmlFor="font_size">Font Size</label>
            <select id="font_size" value={fontSize} onChange={(e) => setFontSize(e.target.value)}>
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
        </div>
        <div>
          <h2 className="text-lg font-semibold">Behavior</h2>
          <div className="flex items-center justify-between">
            <label htmlFor="auto_scroll">Auto-scroll</label>
            <input id="auto_scroll" type="checkbox" checked={autoScroll} onChange={(e) => setAutoScroll(e.target.checked)} />
          </div>
          <div className="flex items-center justify-between">
            <label htmlFor="typing_indicators">Enable typing indicators</label>
            <input id="typing_indicators" type="checkbox" checked={typingIndicators} onChange={(e) => setTypingIndicators(e.target.checked)} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatSettings;