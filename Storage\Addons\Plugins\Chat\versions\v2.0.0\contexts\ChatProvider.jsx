import React, { createContext, useState, useEffect, useRef, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

export const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState(null);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);
  const [modelError, setModelError] = useState(null);
  const [preloadingError, setPreloadingError] = useState(null);

  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const addMessage = useCallback((message) => {
    if (!isMounted.current) return;
    setMessages(prev => [...prev, { ...message, id: message.id || `msg_${Date.now()}` }]);
  }, []);

  const fetchAvailableModels = useCallback(async () => {
    if (!isMounted.current) return;
    setIsLoadingModels(true);
    setModelError(null);
    try {
      const models = await invoke('get_available_models_command');
      if (isMounted.current) {
        setAvailableModels(models);
        if (models.length > 0) {
          setSelectedModel(models[0]);
        }
      }
    } catch (error) {
      if (isMounted.current) {
        setModelError('Failed to fetch models.');
      }
      console.error('Failed to fetch available models:', error);
    } finally {
      if (isMounted.current) {
        setIsLoadingModels(false);
      }
    }
  }, []);

  const preloadModel = useCallback(async (modelName) => {
    if (!isMounted.current) return;
    setIsPreloading(true);
    setPreloadingError(null);
    try {
      await invoke('preload_model_command', { modelName });
    } catch (error) {
      if (isMounted.current) {
        setPreloadingError(`Failed to preload model: ${modelName}`);
      }
      console.error(`Failed to preload model ${modelName}:`, error);
    } finally {
      if (isMounted.current) {
        setIsPreloading(false);
      }
    }
  }, []);

  useEffect(() => {
    fetchAvailableModels();
  }, [fetchAvailableModels]);

  useEffect(() => {
    if (selectedModel) {
      preloadModel(selectedModel);
    }
  }, [selectedModel, preloadModel]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const trimmedInput = input.trim();
    if (!trimmedInput || isLoading || isPreloading || !isMounted.current) return;

    if (!selectedModel) {
      addMessage({ content: 'Please select a model first.', sender: 'system', isError: true });
      return;
    }

    const userMessage = {
      id: `msg_${Date.now()}`,
      content: trimmedInput,
      sender: 'user',
      timestamp: new Date(),
      model_used: selectedModel
    };

    const assistantMessage = {
        id: `msg_${Date.now() + 1}`,
        content: '',
        sender: 'assistant',
        timestamp: new Date(),
        model_used: selectedModel,
        isTyping: true,
    };

    setMessages(prev => [...prev, userMessage, assistantMessage]);
    setInput('');
    setIsLoading(true);

    try {
      await invoke('send_chat_message_command', {
        modelName: selectedModel,
        prompt: trimmedInput
      });
    } catch (error) {
      console.error('Error sending message:', error);
      if (isMounted.current) {
        addMessage({ content: `Error: ${error}`, sender: 'system', isError: true });
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    const unlistenToken = listen('chat_token', (event) => {
      const { content } = event.payload;
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        if (lastMessage && lastMessage.sender === 'assistant') {
          lastMessage.content += content;
          lastMessage.isTyping = false;
        }
        return newMessages;
      });
    });

    const unlistenEnd = listen('chat_token_end', () => {
      setIsLoading(false);
    });

    const unlistenError = listen('chat_error', (event) => {
      const errorMessage = event.payload;
      addMessage({
        id: `err_${Date.now()}`,
        content: `Error: ${errorMessage}`,
        sender: 'system',
        isError: true,
      });
      setIsLoading(false);
    });

    return () => {
      unlistenToken.then(f => f());
      unlistenEnd.then(f => f());
      unlistenError.then(f => f());
    };
  }, [addMessage]);

  const value = {
    messages,
    input,
    setInput,
    isLoading,
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    isPreloading,
    modelError,
    preloadingError,
    fetchAvailableModels,
    preloadModel,
    handleSubmit,
    addMessage
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};